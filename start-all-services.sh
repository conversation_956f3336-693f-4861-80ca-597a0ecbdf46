#!/bin/bash

# 启动所有独立Docker服务的脚本
# 按照依赖顺序启动5个独立的服务

set -e

echo "=== 大语言模型管理系统 - 独立服务启动脚本 ==="
echo ""

# 检查Docker和Docker Compose是否安装
if ! command -v docker &> /dev/null; then
    echo "错误: Docker 未安装或未在PATH中"
    exit 1
fi

if ! docker compose version &> /dev/null; then
    echo "错误: Docker Compose 未安装或未在PATH中"
    exit 1
fi

# 检查数据目录是否存在，如果不存在则创建
echo "检查数据目录..."
if [ ! -d "/vdb_models" ]; then
    echo "创建模型数据库目录: /vdb_models"
    sudo mkdir -p /vdb_models
    sudo chown -R 999:999 /vdb_models
fi

if [ ! -d "/vdb_records" ]; then
    echo "创建聊天记录数据库目录: /vdb_records"
    sudo mkdir -p /vdb_records
    sudo chown -R 999:999 /vdb_records
fi

echo "数据目录检查完成"
echo ""

# 停止现有服务（如果存在）
echo "停止现有服务..."
(cd models-database && docker compose down 2>/dev/null) || true
(cd records-database && docker compose down 2>/dev/null) || true
(cd model-manager && docker compose down 2>/dev/null) || true
(cd model-api-service && docker compose down 2>/dev/null) || true
(cd record-api-service && docker compose down 2>/dev/null) || true
echo ""

# 按依赖顺序启动服务

echo "=== 1. 启动模型数据库服务 ==="
(cd models-database && docker compose up -d)
echo "等待模型数据库启动..."
sleep 15
echo ""

echo "=== 2. 启动聊天记录数据库服务 ==="
(cd records-database && docker compose up -d)
echo "等待聊天记录数据库启动..."
sleep 15
echo ""

echo "=== 3. 启动模型管理服务 ==="
(cd model-manager && docker compose up -d --build)
echo "等待模型管理服务启动..."
sleep 20
echo ""

echo "=== 4. 启动模型API服务 ==="
(cd model-api-service && docker compose up -d --build)
echo "等待模型API服务启动..."
sleep 20
echo ""

echo "=== 5. 启动记录API服务 ==="
(cd record-api-service && docker compose up -d --build)
echo "等待记录API服务启动..."
sleep 20
echo ""

# 检查服务状态
echo "=== 检查服务状态 ==="
echo "模型数据库服务:"
(cd models-database && docker compose ps)
echo ""

echo "聊天记录数据库服务:"
(cd records-database && docker compose ps)
echo ""

echo "模型管理服务:"
(cd model-manager && docker compose ps)
echo ""

echo "模型API服务:"
(cd model-api-service && docker compose ps)
echo ""

echo "记录API服务:"
(cd record-api-service && docker compose ps)
echo ""

# 显示服务访问信息
echo "=== 服务访问信息 ==="
echo "1. 模型数据库: localhost:3306 (用户: models_user)"
echo "2. 聊天记录数据库: localhost:3307 (用户: records_user)"
echo "3. 模型管理服务: http://localhost:5001"
echo "4. 模型API服务: http://localhost:5002"
echo "5. 记录API服务: http://localhost:5003"
echo ""

# 显示健康检查状态
echo "=== 健康检查状态 ==="
echo "等待服务完全启动..."
sleep 30

echo "检查模型管理服务健康状态..."
curl -f http://localhost:5001/api/v1/health 2>/dev/null && echo "✓ 模型管理服务正常" || echo "✗ 模型管理服务异常"

echo "检查模型API服务健康状态..."
curl -f http://localhost:5002/api/v1/health 2>/dev/null && echo "✓ 模型API服务正常" || echo "✗ 模型API服务异常"

echo "检查记录API服务健康状态..."
curl -f http://localhost:5003/api/v1/health 2>/dev/null && echo "✓ 记录API服务正常" || echo "✗ 记录API服务异常"

echo ""
echo "=== 启动完成 ==="
echo "所有服务已启动，可以开始使用系统"
echo ""
echo "常用命令:"
echo "  查看所有服务状态: ./check-services.sh"
echo "  停止所有服务: ./stop-all-services.sh"
echo "  查看服务日志: cd <service-dir> && docker-compose logs -f"
