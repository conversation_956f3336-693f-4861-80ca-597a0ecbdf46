#!/bin/bash

# 停止所有独立Docker服务的脚本

set -e

echo "=== 停止所有服务 ==="
echo ""

# 按相反顺序停止服务（先停止依赖服务）

echo "停止API服务..."
(cd model-api-service && docker compose down)
(cd record-api-service && docker compose down)

echo "停止模型管理服务..."
(cd model-manager && docker compose down)

echo "停止聊天记录数据库服务..."
(cd records-database && docker compose down)

echo "停止模型数据库服务..."
(cd models-database && docker compose down)

echo ""
echo "=== 所有服务已停止 ==="
