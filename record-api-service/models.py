"""
聊天记录数据库模型
"""
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from decimal import Decimal

db = SQLAlchemy()

class User(db.Model):
    """用户表"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    api_key = db.Column(db.String(255), nullable=False, unique=True, index=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    is_active = db.Column(db.Boolean, default=True, index=True)
    permission = db.Column(db.Integer, default=1, nullable=False, index=True)
    mathjax = db.Column(db.Bo<PERSON>an, default=False)
    current_model_id = db.Column(db.Integer)
    current_temperature = db.Column(db.Numeric(3, 2), default=Decimal('0.70'))
    current_conversation_id = db.Column(db.<PERSON>, db.<PERSON><PERSON>('conversations.id'))
    total_deposited = db.Column(db.Numeric(10, 4), default=Decimal('0.0000'))
    total_spent = db.Column(db.Numeric(10, 4), default=Decimal('0.0000'))
    current_balance = db.Column(db.Numeric(10, 4), default=Decimal('0.0000'))
    total_prompt_tokens = db.Column(db.Integer, default=0)
    total_completion_tokens = db.Column(db.Integer, default=0)
    
    # 关系
    conversations = db.relationship('Conversation', backref='user', lazy=True, 
                                  foreign_keys='Conversation.user_id')
    current_conversation = db.relationship('Conversation', 
                                         foreign_keys=[current_conversation_id],
                                         post_update=True)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'api_key': self.api_key,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'is_active': self.is_active,
            'permission': self.permission,
            'mathjax': self.mathjax,
            'current_model_id': self.current_model_id,
            'current_temperature': float(self.current_temperature) if self.current_temperature else None,
            'current_conversation_id': self.current_conversation_id,
            'total_deposited': float(self.total_deposited) if self.total_deposited else 0.0,
            'total_spent': float(self.total_spent) if self.total_spent else 0.0,
            'current_balance': float(self.current_balance) if self.current_balance else 0.0,
            'total_prompt_tokens': self.total_prompt_tokens,
            'total_completion_tokens': self.total_completion_tokens
        }

class Conversation(db.Model):
    """对话表"""
    __tablename__ = 'conversations'
    
    id = db.Column(db.Integer, primary_key=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    latest_revised_at = db.Column(db.DateTime, default=datetime.utcnow, 
                                onupdate=datetime.utcnow, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    title = db.Column(db.String(255))
    
    # 关系
    messages = db.relationship('Message', backref='conversation', lazy=True, 
                             cascade='all, delete-orphan')
    
    def to_dict(self, include_messages=False):
        """转换为字典"""
        result = {
            'id': self.id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'latest_revised_at': self.latest_revised_at.isoformat() if self.latest_revised_at else None,
            'user_id': self.user_id,
            'title': self.title,
            'message_count': len(self.messages) if self.messages else 0
        }
        
        if include_messages:
            result['messages'] = [msg.to_dict() for msg in self.messages]
            
        return result

class Message(db.Model):
    """消息表"""
    __tablename__ = 'messages'
    
    id = db.Column(db.Integer, primary_key=True)
    conversation_id = db.Column(db.Integer, db.ForeignKey('conversations.id'), 
                               nullable=False, index=True)
    role = db.Column(db.String(20), index=True)
    content = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, 
                          onupdate=datetime.utcnow)
    model_id = db.Column(db.Integer, nullable=False, index=True)
    temperature = db.Column(db.Numeric(3, 2))
    max_tokens = db.Column(db.Integer)
    prompt_tokens = db.Column(db.Integer)
    completion_tokens = db.Column(db.Integer)
    prompt_cost = db.Column(db.Numeric(10, 6), default=Decimal('0.000000'))
    completion_cost = db.Column(db.Numeric(10, 6), default=Decimal('0.000000'))
    total_cost = db.Column(db.Numeric(10, 6), default=Decimal('0.000000'))
    is_error = db.Column(db.Boolean, default=False, index=True)
    error_info = db.Column(db.Text)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'conversation_id': self.conversation_id,
            'role': self.role,
            'content': self.content,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'model_id': self.model_id,
            'temperature': float(self.temperature) if self.temperature else None,
            'max_tokens': self.max_tokens,
            'prompt_tokens': self.prompt_tokens,
            'completion_tokens': self.completion_tokens,
            'prompt_cost': float(self.prompt_cost) if self.prompt_cost else 0.0,
            'completion_cost': float(self.completion_cost) if self.completion_cost else 0.0,
            'total_cost': float(self.total_cost) if self.total_cost else 0.0,
            'is_error': self.is_error,
            'error_info': self.error_info
        }

class SystemInfo(db.Model):
    """系统信息表"""
    __tablename__ = 'system_info'
    
    id = db.Column(db.Integer, primary_key=True)
    version = db.Column(db.String(20), nullable=False)
    initialized_at = db.Column(db.DateTime, default=datetime.utcnow)
    notes = db.Column(db.Text)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'version': self.version,
            'initialized_at': self.initialized_at.isoformat() if self.initialized_at else None,
            'notes': self.notes
        }
