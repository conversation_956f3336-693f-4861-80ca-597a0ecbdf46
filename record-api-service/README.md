# 记录API服务 (Record API Service)

这是大语言模型管理系统的记录管理组件：一个专门用于管理用户、对话和消息记录的RESTful API服务，运行在5003端口。

## 核心功能

- **用户管理**：创建、查询、更新和删除用户信息，管理用户权限和余额
- **对话管理**：管理用户的对话会话，包括对话创建、更新和删除
- **消息管理**：存储和检索聊天消息，支持批量操作和搜索功能
- **统计分析**：提供用户和对话的统计信息，包括token使用量和费用统计
- **安全认证**：所有请求必须通过 `Authorization: Bearer <API_KEY>` 头进行认证

## 数据库连接

- **主数据库**: 连接到 `records-database` (端口3307) 的 `vdb_records` 数据库
- **用户**: records_user
- **密码**: records_password

## API 端点概览

### 用户管理 API

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/api/v1/users` | 获取用户列表 |
| GET | `/api/v1/users/{id}` | 获取单个用户信息 |
| POST | `/api/v1/users` | 创建新用户 |
| PUT | `/api/v1/users/{id}` | 更新用户信息 |
| DELETE | `/api/v1/users/{id}` | 删除用户 |
| GET | `/api/v1/users/by-api-key/{api_key}` | 根据API密钥获取用户 |
| GET | `/api/v1/users/{id}/stats` | 获取用户统计信息 |

### 对话管理 API

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/api/v1/conversations` | 获取对话列表 |
| GET | `/api/v1/conversations/{id}` | 获取单个对话信息 |
| POST | `/api/v1/conversations` | 创建新对话 |
| PUT | `/api/v1/conversations/{id}` | 更新对话信息 |
| DELETE | `/api/v1/conversations/{id}` | 删除对话 |
| GET | `/api/v1/users/{user_id}/conversations` | 获取用户的对话列表 |
| GET | `/api/v1/conversations/{id}/messages` | 获取对话的消息列表 |
| GET | `/api/v1/conversations/{id}/stats` | 获取对话统计信息 |

### 消息管理 API

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/api/v1/messages` | 获取消息列表 |
| GET | `/api/v1/messages/{id}` | 获取单个消息信息 |
| POST | `/api/v1/messages` | 创建新消息 |
| PUT | `/api/v1/messages/{id}` | 更新消息信息 |
| DELETE | `/api/v1/messages/{id}` | 删除消息 |
| POST | `/api/v1/messages/batch` | 批量创建消息 |
| GET | `/api/v1/messages/search` | 搜索消息 |

### 系统 API

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/health` | 健康检查 |
| GET | `/api/v1/health` | API健康检查 |
| GET | `/api/v1/info` | API信息 |

## 认证方式

所有API请求都需要在请求头中包含有效的API密钥：

```
Authorization: Bearer YOUR_API_KEY
```

或者作为查询参数：

```
?api_key=YOUR_API_KEY
```

### 默认管理员账号

系统会自动创建一个默认的管理员账号：

- **API密钥**: `admin-api-key-change-in-production`
- **权限级别**: 9 (管理员)
- **状态**: 激活

⚠️ **重要**: 在生产环境中，请务必修改默认的管理员API密钥！

### 权限级别说明

- **1**: 普通用户 (默认权限)
- **2-8**: 高级用户 (扩展权限)
- **9**: 管理员 (最高权限)

## 响应格式

所有API响应都采用统一的JSON格式：

### 成功响应
```json
{
  "success": true,
  "data": {
    // 响应数据
  }
}
```

### 错误响应
```json
{
  "success": false,
  "error": "错误描述"
}
```

### 分页响应
```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "per_page": 20,
      "total": 100,
      "pages": 5,
      "has_prev": false,
      "has_next": true,
      "prev_num": null,
      "next_num": 2
    }
  }
}
```

## 查询参数

### 分页参数
- `page`: 页码 (默认: 1)
- `per_page`: 每页数量 (默认: 20, 最大: 100)

### 排序参数
- `sort_by`: 排序字段
- `sort_order`: 排序方向 (`asc` 或 `desc`)

### 过滤参数
根据不同的API端点，支持不同的过滤参数。

## 使用示例

### 1. 获取用户列表

```bash
curl -X GET "http://localhost:5003/api/v1/users?page=1&per_page=10" \
     -H "Authorization: Bearer admin-api-key-change-in-production"
```

### 2. 创建新用户

```bash
curl -X POST "http://localhost:5003/api/v1/users" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer admin-api-key-change-in-production" \
     -d '{
       "api_key": "user-api-key-123",
       "permission": 1,
       "current_balance": 10.0
     }'
```

### 3. 创建对话

```bash
curl -X POST "http://localhost:5003/api/v1/conversations" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_API_KEY" \
     -d '{
       "user_id": 1,
       "title": "新的对话"
     }'
```

### 4. 添加消息

```bash
curl -X POST "http://localhost:5003/api/v1/messages" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_API_KEY" \
     -d '{
       "conversation_id": 1,
       "role": "user",
       "content": "你好，请介绍一下自己。",
       "model_id": 1
     }'
```

### 5. 搜索消息

```bash
curl -X GET "http://localhost:5003/api/v1/messages/search?keyword=介绍&page=1" \
     -H "Authorization: Bearer YOUR_API_KEY"
```

### 6. 获取用户统计

```bash
curl -X GET "http://localhost:5003/api/v1/users/1/stats" \
     -H "Authorization: Bearer YOUR_API_KEY"
```

## Python 客户端示例

```python
import requests

class RecordAPIClient:
    def __init__(self, base_url, api_key):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
    
    def get_users(self, page=1, per_page=20):
        """获取用户列表"""
        response = requests.get(
            f"{self.base_url}/api/v1/users",
            headers=self.headers,
            params={'page': page, 'per_page': per_page}
        )
        return response.json()
    
    def create_user(self, api_key, **kwargs):
        """创建用户"""
        data = {'api_key': api_key, **kwargs}
        response = requests.post(
            f"{self.base_url}/api/v1/users",
            headers=self.headers,
            json=data
        )
        return response.json()
    
    def create_conversation(self, user_id, title=None):
        """创建对话"""
        data = {'user_id': user_id}
        if title:
            data['title'] = title
        response = requests.post(
            f"{self.base_url}/api/v1/conversations",
            headers=self.headers,
            json=data
        )
        return response.json()
    
    def add_message(self, conversation_id, role, content, model_id, **kwargs):
        """添加消息"""
        data = {
            'conversation_id': conversation_id,
            'role': role,
            'content': content,
            'model_id': model_id,
            **kwargs
        }
        response = requests.post(
            f"{self.base_url}/api/v1/messages",
            headers=self.headers,
            json=data
        )
        return response.json()

# 使用示例
client = RecordAPIClient('http://localhost:5003', 'admin-api-key-change-in-production')

# 获取用户列表
users = client.get_users()
print(users)

# 创建用户
new_user = client.create_user('user-123', permission=1, current_balance=10.0)
print(new_user)
```

## 配置

- **端口**: 5003
- **数据库**: 连接到 `records-database` 的 `vdb_records` 数据库
- **核心依赖**: `Flask`, `SQLAlchemy`, `PyMySQL`, `gunicorn`

## 启动服务

### 使用Docker Compose

```bash
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 本地开发

```bash
# 安装依赖
pip install -r requirements.txt

# 设置环境变量
export FLASK_ENV=development
export DATABASE_URL=mysql+pymysql://records_user:records_password@localhost:3307/vdb_records

# 启动服务
python app.py
```

### 测试管理员账号

使用提供的测试脚本验证管理员账号是否正确配置：

```bash
# 确保服务正在运行
docker-compose up -d

# 运行测试脚本
python test_admin.py
```

测试脚本会验证：
- 服务健康状态
- 管理员账号是否存在
- 管理员权限是否正确
- 认证机制是否工作正常

## 与其他服务的关系

- **records-database**: 主要数据存储，存储用户、对话和消息数据
- **model-api-service**: 可以调用本服务来记录API调用历史
- **model-manager**: 可以查询本服务获取用户使用统计

## 注意事项

1. **数据库依赖**: 确保 `records-database` 服务已启动并可访问
2. **管理员账号**: 系统会自动创建默认管理员账号，生产环境请务必修改默认API密钥
3. **API密钥管理**: 在生产环境中请使用安全的API密钥
4. **权限控制**: 管理员权限级别为9，拥有所有API的访问权限
5. **数据备份**: 定期备份重要的聊天记录数据
6. **性能监控**: 监控API响应时间和数据库连接状态
7. **日志管理**: 定期清理和归档日志文件

## 详细API文档

### 用户管理 API

#### GET /api/v1/users
获取用户列表，支持分页和过滤。

**查询参数:**
- `page` (int): 页码，默认1
- `per_page` (int): 每页数量，默认20，最大100
- `is_active` (bool): 过滤活跃用户
- `permission` (int): 过滤权限级别
- `sort_by` (string): 排序字段，默认created_at
- `sort_order` (string): 排序方向，asc或desc，默认desc

**响应示例:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": 1,
        "api_key": "user-api-key-123",
        "created_at": "2024-01-01T00:00:00",
        "is_active": true,
        "permission": 1,
        "current_balance": 10.0,
        "total_spent": 5.5,
        "total_prompt_tokens": 1000,
        "total_completion_tokens": 800
      }
    ],
    "pagination": {
      "page": 1,
      "per_page": 20,
      "total": 1,
      "pages": 1,
      "has_prev": false,
      "has_next": false
    }
  }
}
```

#### POST /api/v1/users
创建新用户。

**请求体:**
```json
{
  "api_key": "user-api-key-123",  // 必需
  "is_active": true,              // 可选，默认true
  "permission": 1,                // 可选，默认1
  "mathjax": false,              // 可选，默认false
  "current_model_id": 1,         // 可选
  "current_temperature": 0.7,    // 可选，默认0.7
  "total_deposited": 10.0,       // 可选，默认0.0
  "current_balance": 10.0        // 可选，默认0.0
}
```

#### PUT /api/v1/users/{id}
更新用户信息。

**请求体:** (所有字段都是可选的)
```json
{
  "is_active": false,
  "permission": 2,
  "current_balance": 15.0
}
```

### 对话管理 API

#### GET /api/v1/conversations
获取对话列表。

**查询参数:**
- `page` (int): 页码
- `per_page` (int): 每页数量
- `user_id` (int): 过滤特定用户的对话
- `include_messages` (bool): 是否包含消息，默认false
- `sort_by` (string): 排序字段，默认latest_revised_at
- `sort_order` (string): 排序方向

#### POST /api/v1/conversations
创建新对话。

**请求体:**
```json
{
  "user_id": 1,                    // 必需
  "title": "关于AI的讨论"          // 可选
}
```

### 消息管理 API

#### GET /api/v1/messages
获取消息列表。

**查询参数:**
- `conversation_id` (int): 过滤特定对话的消息
- `role` (string): 过滤消息角色 (user, assistant, system)
- `model_id` (int): 过滤特定模型的消息
- `is_error` (bool): 过滤错误消息

#### POST /api/v1/messages
创建新消息。

**请求体:**
```json
{
  "conversation_id": 1,           // 必需
  "role": "user",                 // 必需
  "content": "你好",              // 必需
  "model_id": 1,                  // 必需
  "temperature": 0.7,             // 可选
  "max_tokens": 1000,             // 可选
  "prompt_tokens": 10,            // 可选
  "completion_tokens": 20,        // 可选
  "prompt_cost": 0.001,           // 可选
  "completion_cost": 0.002,       // 可选
  "total_cost": 0.003,            // 可选
  "is_error": false,              // 可选
  "error_info": null              // 可选
}
```

#### POST /api/v1/messages/batch
批量创建消息。

**请求体:**
```json
{
  "messages": [
    {
      "conversation_id": 1,
      "role": "user",
      "content": "你好",
      "model_id": 1
    },
    {
      "conversation_id": 1,
      "role": "assistant",
      "content": "你好！我是AI助手。",
      "model_id": 1,
      "prompt_tokens": 5,
      "completion_tokens": 15
    }
  ]
}
```

#### GET /api/v1/messages/search
搜索消息内容。

**查询参数:**
- `keyword` (string): 搜索关键词，必需
- `conversation_id` (int): 限制在特定对话中搜索
- `user_id` (int): 限制在特定用户的消息中搜索
- `role` (string): 限制消息角色

## 错误代码

| 状态码 | 描述 |
|--------|------|
| 200 | 成功 |
| 201 | 创建成功 |
| 400 | 请求格式错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源未找到 |
| 405 | 方法不允许 |
| 500 | 服务器内部错误 |

## 数据模型

### User (用户)
```json
{
  "id": 1,
  "api_key": "string",
  "created_at": "datetime",
  "is_active": "boolean",
  "permission": "integer",
  "mathjax": "boolean",
  "current_model_id": "integer",
  "current_temperature": "decimal",
  "current_conversation_id": "integer",
  "total_deposited": "decimal",
  "total_spent": "decimal",
  "current_balance": "decimal",
  "total_prompt_tokens": "integer",
  "total_completion_tokens": "integer"
}
```

### Conversation (对话)
```json
{
  "id": 1,
  "created_at": "datetime",
  "latest_revised_at": "datetime",
  "user_id": "integer",
  "title": "string",
  "message_count": "integer"
}
```

### Message (消息)
```json
{
  "id": 1,
  "conversation_id": "integer",
  "role": "string",
  "content": "text",
  "created_at": "datetime",
  "updated_at": "datetime",
  "model_id": "integer",
  "temperature": "decimal",
  "max_tokens": "integer",
  "prompt_tokens": "integer",
  "completion_tokens": "integer",
  "prompt_cost": "decimal",
  "completion_cost": "decimal",
  "total_cost": "decimal",
  "is_error": "boolean",
  "error_info": "text"
}
```
