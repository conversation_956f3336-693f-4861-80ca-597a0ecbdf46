"""
认证和授权工具函数
"""
from functools import wraps
from flask import request, jsonify, current_app
import os

def require_api_key(f):
    """
    API密钥认证装饰器
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 获取API密钥
        api_key = None
        
        # 从Authorization头获取
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            api_key = auth_header[7:]  # 移除 "Bearer " 前缀
        
        # 从查询参数获取（备用方式）
        if not api_key:
            api_key = request.args.get('api_key')
        
        # 验证API密钥
        if not api_key:
            return jsonify({
                'success': False,
                'error': '缺少API密钥'
            }), 401
        
        # 这里可以添加更复杂的API密钥验证逻辑
        # 例如从数据库验证、检查权限等
        valid_api_keys = get_valid_api_keys()
        
        if api_key not in valid_api_keys:
            return jsonify({
                'success': False,
                'error': 'API密钥无效'
            }), 401
        
        # 将API密钥添加到请求上下文
        request.api_key = api_key
        
        return f(*args, **kwargs)
    
    return decorated_function

def get_valid_api_keys():
    """
    获取有效的API密钥列表
    
    在实际应用中，这应该从数据库或配置文件中获取
    """
    # 从环境变量获取管理员API密钥
    admin_api_key = os.environ.get('ADMIN_API_KEY', 'admin-api-key-change-in-production')
    
    # 从配置获取其他有效密钥
    valid_keys = [admin_api_key]
    
    # 可以添加从数据库获取用户API密钥的逻辑
    # 例如：
    # from models import User
    # user_keys = [user.api_key for user in User.query.filter_by(is_active=True).all()]
    # valid_keys.extend(user_keys)
    
    return valid_keys

def check_permission(required_permission):
    """
    权限检查装饰器
    
    Args:
        required_permission: 所需权限级别
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 这里可以添加权限检查逻辑
            # 例如从数据库获取用户权限并验证
            
            # 简单示例：检查是否为管理员API密钥
            api_key = getattr(request, 'api_key', None)
            admin_api_key = os.environ.get('ADMIN_API_KEY', 'admin-api-key-change-in-production')
            
            if required_permission > 1 and api_key != admin_api_key:
                return jsonify({
                    'success': False,
                    'error': '权限不足'
                }), 403
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def get_current_user():
    """
    获取当前用户信息
    
    Returns:
        dict: 用户信息，如果未找到则返回None
    """
    api_key = getattr(request, 'api_key', None)
    if not api_key:
        return None
    
    # 这里可以添加从数据库获取用户信息的逻辑
    # 例如：
    # from models import User
    # user = User.query.filter_by(api_key=api_key, is_active=True).first()
    # return user.to_dict() if user else None
    
    # 简单示例
    admin_api_key = os.environ.get('ADMIN_API_KEY', 'admin-api-key-change-in-production')
    if api_key == admin_api_key:
        return {
            'id': 0,
            'api_key': api_key,
            'permission': 999,  # 管理员权限
            'is_admin': True
        }
    
    return None

def rate_limit_check(max_requests=100, window_seconds=3600):
    """
    速率限制检查装饰器
    
    Args:
        max_requests: 最大请求次数
        window_seconds: 时间窗口（秒）
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 这里可以添加速率限制逻辑
            # 例如使用Redis或内存缓存来跟踪请求频率
            
            # 简单示例：总是允许（在生产环境中应该实现真正的速率限制）
            if current_app.config.get('RATE_LIMIT_ENABLED', True):
                # TODO: 实现真正的速率限制逻辑
                pass
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator
