"""
消息管理API蓝图
"""
from flask import Blueprint, request, jsonify
from sqlalchemy import desc, asc
from models import db, Message, Conversation
from utils.helpers import paginate_query, validate_json_data
from utils.auth import require_api_key
from decimal import Decimal

messages_bp = Blueprint('messages', __name__)

@messages_bp.route('/messages', methods=['GET'])
@require_api_key
def get_messages():
    """获取消息列表"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 50, type=int), 100)
        conversation_id = request.args.get('conversation_id', type=int)
        role = request.args.get('role')
        model_id = request.args.get('model_id', type=int)
        is_error = request.args.get('is_error', type=bool)
        sort_by = request.args.get('sort_by', 'created_at')
        sort_order = request.args.get('sort_order', 'desc')
        
        # 构建查询
        query = Message.query
        
        # 过滤条件
        if conversation_id:
            query = query.filter(Message.conversation_id == conversation_id)
        if role:
            query = query.filter(Message.role == role)
        if model_id:
            query = query.filter(Message.model_id == model_id)
        if is_error is not None:
            query = query.filter(Message.is_error == is_error)
            
        # 排序
        if sort_order == 'desc':
            query = query.order_by(desc(getattr(Message, sort_by, Message.created_at)))
        else:
            query = query.order_by(asc(getattr(Message, sort_by, Message.created_at)))
        
        # 分页
        result = paginate_query(query, page, per_page)
        
        return jsonify({
            'success': True,
            'data': {
                'messages': [msg.to_dict() for msg in result['items']],
                'pagination': result['pagination']
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@messages_bp.route('/messages/<int:message_id>', methods=['GET'])
@require_api_key
def get_message(message_id):
    """获取单个消息信息"""
    try:
        message = Message.query.get_or_404(message_id)
        return jsonify({
            'success': True,
            'data': message.to_dict()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@messages_bp.route('/messages', methods=['POST'])
@require_api_key
def create_message():
    """创建新消息"""
    try:
        data = validate_json_data(request, required_fields=['conversation_id', 'role', 'content', 'model_id'])
        
        # 验证对话是否存在
        conversation = Conversation.query.get_or_404(data['conversation_id'])
        
        # 创建消息
        message = Message(
            conversation_id=data['conversation_id'],
            role=data['role'],
            content=data['content'],
            model_id=data['model_id'],
            temperature=Decimal(str(data.get('temperature'))) if data.get('temperature') is not None else None,
            max_tokens=data.get('max_tokens'),
            prompt_tokens=data.get('prompt_tokens'),
            completion_tokens=data.get('completion_tokens'),
            prompt_cost=Decimal(str(data.get('prompt_cost', 0.0))),
            completion_cost=Decimal(str(data.get('completion_cost', 0.0))),
            total_cost=Decimal(str(data.get('total_cost', 0.0))),
            is_error=data.get('is_error', False),
            error_info=data.get('error_info')
        )
        
        db.session.add(message)
        
        # 更新对话的最后修改时间
        conversation.latest_revised_at = message.created_at
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'data': message.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@messages_bp.route('/messages/<int:message_id>', methods=['PUT'])
@require_api_key
def update_message(message_id):
    """更新消息信息"""
    try:
        message = Message.query.get_or_404(message_id)
        data = validate_json_data(request)
        
        # 更新字段
        if 'content' in data:
            message.content = data['content']
        if 'role' in data:
            message.role = data['role']
        if 'model_id' in data:
            message.model_id = data['model_id']
        if 'temperature' in data:
            message.temperature = Decimal(str(data['temperature'])) if data['temperature'] is not None else None
        if 'max_tokens' in data:
            message.max_tokens = data['max_tokens']
        if 'prompt_tokens' in data:
            message.prompt_tokens = data['prompt_tokens']
        if 'completion_tokens' in data:
            message.completion_tokens = data['completion_tokens']
        if 'prompt_cost' in data:
            message.prompt_cost = Decimal(str(data['prompt_cost']))
        if 'completion_cost' in data:
            message.completion_cost = Decimal(str(data['completion_cost']))
        if 'total_cost' in data:
            message.total_cost = Decimal(str(data['total_cost']))
        if 'is_error' in data:
            message.is_error = data['is_error']
        if 'error_info' in data:
            message.error_info = data['error_info']
            
        db.session.commit()
        
        return jsonify({
            'success': True,
            'data': message.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@messages_bp.route('/messages/<int:message_id>', methods=['DELETE'])
@require_api_key
def delete_message(message_id):
    """删除消息"""
    try:
        message = Message.query.get_or_404(message_id)
        db.session.delete(message)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '消息已删除'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@messages_bp.route('/messages/batch', methods=['POST'])
@require_api_key
def create_messages_batch():
    """批量创建消息"""
    try:
        data = validate_json_data(request, required_fields=['messages'])
        messages_data = data['messages']
        
        if not isinstance(messages_data, list) or len(messages_data) == 0:
            return jsonify({
                'success': False,
                'error': '消息列表不能为空'
            }), 400
        
        created_messages = []
        
        for msg_data in messages_data:
            # 验证必需字段
            required_fields = ['conversation_id', 'role', 'content', 'model_id']
            for field in required_fields:
                if field not in msg_data:
                    return jsonify({
                        'success': False,
                        'error': f'消息缺少必需字段: {field}'
                    }), 400
            
            # 验证对话是否存在
            conversation = Conversation.query.get(msg_data['conversation_id'])
            if not conversation:
                return jsonify({
                    'success': False,
                    'error': f'对话 {msg_data["conversation_id"]} 不存在'
                }), 404
            
            # 创建消息
            message = Message(
                conversation_id=msg_data['conversation_id'],
                role=msg_data['role'],
                content=msg_data['content'],
                model_id=msg_data['model_id'],
                temperature=Decimal(str(msg_data.get('temperature'))) if msg_data.get('temperature') is not None else None,
                max_tokens=msg_data.get('max_tokens'),
                prompt_tokens=msg_data.get('prompt_tokens'),
                completion_tokens=msg_data.get('completion_tokens'),
                prompt_cost=Decimal(str(msg_data.get('prompt_cost', 0.0))),
                completion_cost=Decimal(str(msg_data.get('completion_cost', 0.0))),
                total_cost=Decimal(str(msg_data.get('total_cost', 0.0))),
                is_error=msg_data.get('is_error', False),
                error_info=msg_data.get('error_info')
            )
            
            db.session.add(message)
            created_messages.append(message)
            
            # 更新对话的最后修改时间
            conversation.latest_revised_at = message.created_at
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'data': {
                'created_count': len(created_messages),
                'messages': [msg.to_dict() for msg in created_messages]
            }
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@messages_bp.route('/messages/search', methods=['GET'])
@require_api_key
def search_messages():
    """搜索消息"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        keyword = request.args.get('keyword', '').strip()
        conversation_id = request.args.get('conversation_id', type=int)
        user_id = request.args.get('user_id', type=int)
        role = request.args.get('role')
        
        if not keyword:
            return jsonify({
                'success': False,
                'error': '搜索关键词不能为空'
            }), 400
        
        # 构建查询
        query = Message.query.filter(Message.content.contains(keyword))
        
        # 过滤条件
        if conversation_id:
            query = query.filter(Message.conversation_id == conversation_id)
        if role:
            query = query.filter(Message.role == role)
        if user_id:
            query = query.join(Conversation).filter(Conversation.user_id == user_id)
            
        # 按创建时间倒序排列
        query = query.order_by(desc(Message.created_at))
        
        # 分页
        result = paginate_query(query, page, per_page)
        
        return jsonify({
            'success': True,
            'data': {
                'keyword': keyword,
                'messages': [msg.to_dict() for msg in result['items']],
                'pagination': result['pagination']
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
